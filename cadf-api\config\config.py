OSS_TMP_DIR = '/tmp'
XHS_IMAGE_GEN_SET = "cadf:xhs:image_gen_set"
XHS_TEXT_GEN_SET = "cadf:xhs:text_gen_set"
XHS_NOTE_CRAWL_SET = "cadf:xhs:note_crawl_set"
XHS_ACCOUNTS_CRAWL_SET = "cadf:xhs:accounts_crawl_set"
XHS_TASK_URL_VERIFY_SET = "cadf:xhs:task_url_verify_set"
XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET = "cadf:xhs:account_online_status_check_set"

class CostProjects:
    """消费项目配置类，提供更好的使用体验"""

    # 项目常量
    IMAGE_GENERATION = "IMAGE_GENERATION"
    TRAFFIC_SETTLEMENT = "TRAFFIC_SETTLEMENT"

# 消费项目配置
COST_PROJECTS = {
    "IMAGE_GENERATION": "图片生成",
    "TRAFFIC_SETTLEMENT": "流量结算"
}

# 消费项目中文名称列表（用于数据验证）
COST_PROJECT_NAMES = list(COST_PROJECTS.values())
