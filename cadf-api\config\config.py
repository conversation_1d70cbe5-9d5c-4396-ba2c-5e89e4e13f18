OSS_TMP_DIR = '/tmp'
XHS_IMAGE_GEN_SET = "cadf:xhs:image_gen_set"
XHS_TEXT_GEN_SET = "cadf:xhs:text_gen_set"
XHS_NOTE_CRAWL_SET = "cadf:xhs:note_crawl_set"
XHS_ACCOUNTS_CRAWL_SET = "cadf:xhs:accounts_crawl_set"
XHS_TASK_URL_VERIFY_SET = "cadf:xhs:task_url_verify_set"
XHS_ACCOUNT_ONLINE_STATUS_CHECK_SET = "cadf:xhs:account_online_status_check_set"

# 消费项目配置类
class CostProjects:
    """消费项目配置类，提供更好的封装和使用体验"""

    # 项目常量
    IMAGE_GENERATION = "IMAGE_GENERATION"
    TRAFFIC_SETTLEMENT = "TRAFFIC_SETTLEMENT"

    # 项目映射
    _projects = {
        IMAGE_GENERATION: "图片生成",
        TRAFFIC_SETTLEMENT: "流量结算"
    }

    @classmethod
    def get(cls, project_key, default=None):
        """根据项目键获取项目中文名称"""
        return cls._projects.get(project_key, default)

    @classmethod
    def keys(cls):
        """获取所有项目键"""
        return cls._projects.keys()

    @classmethod
    def values(cls):
        """获取所有项目中文名称"""
        return cls._projects.values()

    @classmethod
    def items(cls):
        """获取所有项目键值对"""
        return cls._projects.items()

    @classmethod
    def get_all_projects(cls):
        """获取所有项目的字典形式"""
        return cls._projects.copy()

    @classmethod
    def is_valid_key(cls, project_key):
        """验证项目键是否有效"""
        return project_key in cls._projects

    @classmethod
    def is_valid_name(cls, project_name):
        """验证项目中文名称是否有效"""
        return project_name in cls._projects.values()

    @classmethod
    def get_key_by_name(cls, project_name):
        """根据中文名称获取项目键"""
        for key, name in cls._projects.items():
            if name == project_name:
                return key
        return None

    @classmethod
    def get_choices_list(cls):
        """获取用于数据库模型 choices 的列表"""
        return list(cls._projects.values())

# 创建实例以保持向后兼容性
COST_PROJECTS = CostProjects()

# 消费项目中文名称列表（用于数据验证）
COST_PROJECT_NAMES = list(COST_PROJECTS.values())
